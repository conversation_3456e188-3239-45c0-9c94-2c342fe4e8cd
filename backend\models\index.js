const { Sequelize } = require('sequelize');
const config = require('../config');

const sequelize = new Sequelize(config.DB_URI, {
  dialect: 'postgres',
  logging: false,
});

// Import models
const User = require('./User')(sequelize);
const Todo = require('./Todo')(sequelize);
const Group = require('./Group')(sequelize);
const Post = require('./Post')(sequelize);
const Thread = require('./Thread')(sequelize);
const Message = require('./Message')(sequelize);
const Comment = require('./Comment')(sequelize);
const Event = require('./Event')(sequelize);
const Exam = require('./Exam')(sequelize);
const Feedback = require('./Feedback')(sequelize);
const Mentor = require('./Mentor')(sequelize);
const Notification = require('./Notification')(sequelize);
const Progress = require('./Progress')(sequelize);
const Resource = require('./Resource')(sequelize);
const TrialCourse = require('./TrialCourse')(sequelize);

// Setup associations
const models = {
  User,
  Todo,
  Group,
  Post,
  Thread,
  Message,
  Comment,
  Event,
  Exam,
  Feedback,
  Mentor,
  Notification,
  Progress,
  Resource,
  TrialCourse,
};

// Call associate function for each model
Object.values(models).forEach(model => {
  if (model.associate) {
    model.associate(models);
  }
});

module.exports = {
  sequelize,
  ...models,
}; 