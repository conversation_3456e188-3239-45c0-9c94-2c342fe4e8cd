const express = require('express');
const router = express.Router();
const axios = require('axios');
const config = require('../config');

// Route Chatbot AI Gemini
router.post('/chatbot', async (req, res) => {
  const { prompt } = req.body;
  try {
    // Gọi Google Gemini API (hoặc OpenAI GPT-4 nếu chưa có Gemini)
    const apiKey = config.GOOGLE_API_KEY || process.env.OPENAI_API_KEY;
    if (!apiKey) return res.status(400).json({ message: 'Missing AI API key' });

    // Nếu dùng Gemini
    if (config.GOOGLE_API_KEY) {
      const geminiRes = await axios.post(
        'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=' + apiKey,
        { contents: [{ parts: [{ text: prompt }] }] }
      );
      const text = geminiRes.data.candidates?.[0]?.content?.parts?.[0]?.text || '<PERSON><PERSON>ông c<PERSON> phản hồi từ Gemini.';
      return res.json({ text });
    }
    // Nếu dùng OpenAI GPT-4
    else {
      const openaiRes = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-4',
          messages: [{ role: 'user', content: prompt }],
        },
        { headers: { Authorization: `Bearer ${apiKey}` } }
      );
      const text = openaiRes.data.choices?.[0]?.message?.content || 'Không có phản hồi từ OpenAI.';
      return res.json({ text });
    }
  } catch (err) {
    return res.status(500).json({ message: 'AI API error', error: err.message });
  }
});

module.exports = router; 