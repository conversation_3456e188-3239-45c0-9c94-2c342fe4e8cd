const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Todo = sequelize.define('Todo', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    deadline: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    remindAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    isDone: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    type: {
      type: DataTypes.STRING,
      defaultValue: 'normal',
      validate: {
        isIn: [['normal', 'exam', 'study']],
      },
    },
  }, {
    tableName: 'todos',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  Todo.associate = (models) => {
    // Todo belongs to User
    Todo.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return Todo;
}; 