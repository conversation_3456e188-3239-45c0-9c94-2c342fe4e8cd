{"v": "5.1.6", "fr": 60, "ip": 0, "op": 300, "w": 800, "h": 600, "nm": "xmas_lights", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "orange", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar pathToTrace = thisComp.layer('wire')('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathTan = pathToTrace.tangentOnPath(progress);\n$bm_rt = radiansToDegrees(Math.atan2(pathTan[1], pathTan[0]));"}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2, "x": "var $bm_rt;\nvar pathLayer = thisComp.layer('wire');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathToTrace = pathLayer('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\n$bm_rt = pathLayer.toComp(pathToTrace.pointOnPath(progress));"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Trace Path", "np": 4, "mn": "Pseudo/ADBE Trace Path", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Progress", "mn": "Pseudo/ADBE Trace Path-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1, "x": "var $bm_rt;\nif (thisProperty.propertyGroup(1)('Pseudo/ADBE Trace Path-0002') == true && thisProperty.numKeys > 1) {\n    $bm_rt = thisProperty.loopOut('cycle');\n} else {\n    $bm_rt = value;\n}"}}, {"ty": 7, "nm": "Loop", "mn": "Pseudo/ADBE Trace Path-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}]}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 3, "nm": "S<PERSON>pe Layer 1: Path 1 [1.1.2]", "cl": "1 2", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [728, 276, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.1.1]", "cl": "1 1", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.92, "y": 0}, "n": "0p833_0p833_0p92_0", "t": 0, "s": [397, 276, 0], "e": [397, 397, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "n": "0p833_0p833_0p167_0p167", "t": 60, "s": [397, 397, 0], "e": [397, 397, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.53, "y": 0}, "n": "0p833_0p833_0p53_0", "t": 180, "s": [397, 397, 0], "e": [397, 276, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 190}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, t, v;\ntry {\n    amp = div(effect('Position - Overshoot')('ADBE Slider Control-0001'), 2.5), freq = div(effect('Position - Bounce')('ADBE Slider Control-0001'), 20), decay = div(effect('Position - Friction')('ADBE Slider Control-0001'), 20), n = 0, 0 < numKeys && (n = nearestKey(time).index, key(n).time > time && n--), t = 0 === n ? 0 : time - key(n).time, $bm_rt = 0 < n ? (v = velocityAtTime(sub(key(n).time, div(thisComp.frameDuration, 10))), sum(value, div(mul(mul(div(v, 100), amp), Math.sin(mul(mul(mul(freq, t), 2), Math.PI))), Math.exp(mul(decay, t))))) : value;\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Position - Overshoot", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Bo<PERSON>ce", "np": 3, "mn": "ADBE Slider Control", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}, {"ty": 5, "nm": "Position - Friction", "np": 3, "mn": "ADBE Slider Control", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 40, "ix": 1, "x": "var $bm_rt;\n$bm_rt = clamp(value, 0, 100);"}}]}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 3, "nm": "Shape Layer 1: Path 1 [1.1.0]", "cl": "1 0", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [97, 276, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 3, "nm": "red", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar pathToTrace = thisComp.layer('wire')('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathTan = pathToTrace.tangentOnPath(progress);\n$bm_rt = radiansToDegrees(Math.atan2(pathTan[1], pathTan[0]));"}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2, "x": "var $bm_rt;\nvar pathLayer = thisComp.layer('wire');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathToTrace = pathLayer('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\n$bm_rt = pathLayer.toComp(pathToTrace.pointOnPath(progress));"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Trace Path", "np": 4, "mn": "Pseudo/ADBE Trace Path", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Progress", "mn": "Pseudo/ADBE Trace Path-0001", "ix": 1, "v": {"a": 0, "k": 97, "ix": 1, "x": "var $bm_rt;\nif (thisProperty.propertyGroup(1)('Pseudo/ADBE Trace Path-0002') == true && thisProperty.numKeys > 1) {\n    $bm_rt = thisProperty.loopOut('cycle');\n} else {\n    $bm_rt = value;\n}"}}, {"ty": 7, "nm": "Loop", "mn": "Pseudo/ADBE Trace Path-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}]}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 3, "nm": "green", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar pathToTrace = thisComp.layer('wire')('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathTan = pathToTrace.tangentOnPath(progress);\n$bm_rt = radiansToDegrees(Math.atan2(pathTan[1], pathTan[0]));"}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2, "x": "var $bm_rt;\nvar pathLayer = thisComp.layer('wire');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathToTrace = pathLayer('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\n$bm_rt = pathLayer.toComp(pathToTrace.pointOnPath(progress));"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Trace Path", "np": 4, "mn": "Pseudo/ADBE Trace Path", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Progress", "mn": "Pseudo/ADBE Trace Path-0001", "ix": 1, "v": {"a": 0, "k": 74, "ix": 1, "x": "var $bm_rt;\nif (thisProperty.propertyGroup(1)('Pseudo/ADBE Trace Path-0002') == true && thisProperty.numKeys > 1) {\n    $bm_rt = thisProperty.loopOut('cycle');\n} else {\n    $bm_rt = value;\n}"}}, {"ty": 7, "nm": "Loop", "mn": "Pseudo/ADBE Trace Path-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}]}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 3, "nm": "yellow", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar pathToTrace = thisComp.layer('wire')('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathTan = pathToTrace.tangentOnPath(progress);\n$bm_rt = radiansToDegrees(Math.atan2(pathTan[1], pathTan[0]));"}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2, "x": "var $bm_rt;\nvar pathLayer = thisComp.layer('wire');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathToTrace = pathLayer('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\n$bm_rt = pathLayer.toComp(pathToTrace.pointOnPath(progress));"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Trace Path", "np": 4, "mn": "Pseudo/ADBE Trace Path", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Progress", "mn": "Pseudo/ADBE Trace Path-0001", "ix": 1, "v": {"a": 0, "k": 48, "ix": 1, "x": "var $bm_rt;\nif (thisProperty.propertyGroup(1)('Pseudo/ADBE Trace Path-0002') == true && thisProperty.numKeys > 1) {\n    $bm_rt = thisProperty.loopOut('cycle');\n} else {\n    $bm_rt = value;\n}"}}, {"ty": 7, "nm": "Loop", "mn": "Pseudo/ADBE Trace Path-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}]}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 3, "nm": "blue", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10, "x": "var $bm_rt;\nvar pathToTrace = thisComp.layer('wire')('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathTan = pathToTrace.tangentOnPath(progress);\n$bm_rt = radiansToDegrees(Math.atan2(pathTan[1], pathTan[0]));"}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2, "x": "var $bm_rt;\nvar pathLayer = thisComp.layer('wire');\nvar progress = div(thisLayer.effect('Pseudo/ADBE Trace Path')('Pseudo/ADBE Trace Path-0001'), 100);\nvar pathToTrace = pathLayer('ADBE Root Vectors Group')(1)('ADBE Vectors Group')(1)('ADBE Vector Shape');\n$bm_rt = pathLayer.toComp(pathToTrace.pointOnPath(progress));"}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Trace Path", "np": 4, "mn": "Pseudo/ADBE Trace Path", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Progress", "mn": "Pseudo/ADBE Trace Path-0001", "ix": 1, "v": {"a": 0, "k": 24, "ix": 1, "x": "var $bm_rt;\nif (thisProperty.propertyGroup(1)('Pseudo/ADBE Trace Path-0002') == true && thisProperty.numKeys > 1) {\n    $bm_rt = thisProperty.loopOut('cycle');\n} else {\n    $bm_rt = value;\n}"}}, {"ty": 7, "nm": "Loop", "mn": "Pseudo/ADBE Trace Path-0002", "ix": 2, "v": {"a": 0, "k": 1, "ix": 2}}]}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "wire", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 300, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Shape Layer 1: Path 1 [1.1.0]", "np": 3, "mn": "ADBE Layer Control", "ix": 1, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 4, "ix": 1}}]}, {"ty": 5, "nm": "Shape Layer 1: Path 1 [1.1.1]", "np": 3, "mn": "ADBE Layer Control", "ix": 2, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "S<PERSON>pe Layer 1: Path 1 [1.1.2]", "np": 3, "mn": "ADBE Layer Control", "ix": 3, "en": 1, "ef": [{"ty": 10, "nm": "Layer", "mn": "ADBE Layer Control-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-178, 0], [0, 0]], "o": [[0, 0], [178, 0], [0, 0]], "v": [[-303, -24], [-3, -24], [328, -24]], "c": false}, "ix": 2, "x": "var $bm_rt;\nvar nullLayerNames = [\n        'Shape Layer 1: Path 1 [1.1.0]',\n        'Shape Layer 1: Path 1 [1.1.1]',\n        'Shape Layer 1: Path 1 [1.1.2]'\n    ];\nvar origPath = thisProperty;\nvar origPoints = origPath.points();\nvar origInTang = origPath.inTangents();\nvar origOutTang = origPath.outTangents();\nvar getNullLayers = [];\nfor (var i = 0; i < nullLayerNames.length; i++) {\n    try {\n        getNullLayers.push(effect(nullLayerNames[i])('ADBE Layer Control-0001'));\n    } catch (err) {\n        getNullLayers.push(null);\n    }\n}\nfor (var i = 0; i < getNullLayers.length; i++) {\n    if (getNullLayers[i] != null && getNullLayers[i].index != thisLayer.index) {\n        origPoints[i] = fromCompToSurface(getNullLayers[i].toComp(getNullLayers[i].anchorPoint));\n    }\n}\n$bm_rt = createPath(origPoints, origInTang, origOutTang, origPath.isClosed());"}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.101437908995, 0.133333333333, 0.10443983639, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "light 4", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-2.547, 5.375, 0], "ix": 2}, "a": {"a": 0, "k": [-226.723, 65.375, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Wayfinder Follower", "np": 4, "mn": "Pseudo/WayfinderFollower", "ix": 1, "en": 0, "ef": [{"ty": 0, "nm": "Position", "mn": "Pseudo/WayfinderFollower-0001", "ix": 1, "v": {"a": 0, "k": 100, "ix": 1}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/WayfinderFollower-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7.75, 4.875], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 1, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.239215686275, 0.239215686275, 0.239215686275, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-226.375, 67.813], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4, 0], [0, 0], [0, -5], [-4, -1.5], [-0.5, 3]], "o": [[-4, 0], [0, 0], [0, 5], [4, 1.5], [0.5, -3]], "v": [[-224, 72], [-229.5, 72.5], [-236, 80.5], [-227.5, 98.5], [-217.5, 82.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972549019608, 0.171626296698, 0.064836599313, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 481, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "light 3", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [4.711, 7.375, 0], "ix": 2}, "a": {"a": 0, "k": [-226.723, 65.375, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Wayfinder Follower", "np": 4, "mn": "Pseudo/WayfinderFollower", "ix": 1, "en": 0, "ef": [{"ty": 0, "nm": "Position", "mn": "Pseudo/WayfinderFollower-0001", "ix": 1, "v": {"a": 0, "k": 80, "ix": 1}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/WayfinderFollower-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7.75, 4.875], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 1, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.239215686275, 0.239215686275, 0.239215686275, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-226.375, 67.813], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4, 0], [0, 0], [0, -5], [-4, -1.5], [-0.5, 3]], "o": [[-4, 0], [0, 0], [0, 5], [4, 1.5], [0.5, -3]], "v": [[-224, 72], [-229.5, 72.5], [-236, 80.5], [-227.5, 98.5], [-217.5, 82.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.049580929326, 0.972549019608, 0.31018371582, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 481, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "light 2", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0.124, 7.383, 0], "ix": 2}, "a": {"a": 0, "k": [-226.723, 65.375, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Wayfinder Follower", "np": 4, "mn": "Pseudo/WayfinderFollower", "ix": 1, "en": 0, "ef": [{"ty": 0, "nm": "Position", "mn": "Pseudo/WayfinderFollower-0001", "ix": 1, "v": {"a": 0, "k": 52, "ix": 1}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/WayfinderFollower-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7.75, 4.875], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 1, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.239215686275, 0.239215686275, 0.239215686275, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-226.375, 67.813], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4, 0], [0, 0], [0, -5], [-4, -1.5], [-0.5, 3]], "o": [[-4, 0], [0, 0], [0, 5], [4, 1.5], [0.5, -3]], "v": [[-224, 72], [-229.5, 72.5], [-236, 80.5], [-227.5, 98.5], [-217.5, 82.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972549019608, 0.961690625957, 0.049580929326, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 481, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "light", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-1.161, 7.375, 0], "ix": 2}, "a": {"a": 0, "k": [-226.723, 65.375, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Wayfinder Follower", "np": 4, "mn": "Pseudo/WayfinderFollower", "ix": 1, "en": 0, "ef": [{"ty": 0, "nm": "Position", "mn": "Pseudo/WayfinderFollower-0001", "ix": 1, "v": {"a": 0, "k": 31, "ix": 1}}, {"ty": 0, "nm": "Offset", "mn": "Pseudo/WayfinderFollower-0002", "ix": 2, "v": {"a": 0, "k": 0, "ix": 2}}]}], "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7.75, 4.875], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 1, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.239215686275, 0.239215686275, 0.239215686275, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-226.375, 67.813], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4, 0], [0, 0], [0, -5], [-4, -1.5], [-0.5, 3]], "o": [[-4, 0], [0, 0], [0, 5], [4, 1.5], [0.5, -3]], "v": [[-224, 72], [-229.5, 72.5], [-236, 80.5], [-227.5, 98.5], [-217.5, 82.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.04576700996, 0.612739413392, 0.972549019608, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 481, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "light 6", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-4.616, 6.133, 0], "ix": 2}, "a": {"a": 0, "k": [-226.723, 65.375, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [7.75, 4.875], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 1, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.239215686275, 0.239215686275, 0.239215686275, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-226.375, 67.813], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4, 0], [0, 0], [0, -5], [-4, -1.5], [-0.5, 3]], "o": [[-4, 0], [0, 0], [0, 5], [4, 1.5], [0.5, -3]], "v": [[-224, 72], [-229.5, 72.5], [-236, 80.5], [-227.5, 98.5], [-217.5, 82.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.972549019608, 0.513353355258, 0.064836599313, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Shape 1", "np": 3, "cix": 2, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 481, "st": 0, "bm": 0}], "markers": []}