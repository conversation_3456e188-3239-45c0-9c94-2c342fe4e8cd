const config = require('./config');
const express = require('express');
const cors = require('cors');
const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const jwt = require('jsonwebtoken');
const http = require('http');
const { Server } = require('socket.io');
const { sendPushNotification } = require('./utils/notify');
const { sequelize, User } = require('./models');

const app = express();
app.use(cors());
app.use(express.json());

// Kết nối PostgreSQL
sequelize.authenticate()
  .then(() => {
    console.log('PostgreSQL connected successfully.');
    return sequelize.sync({ force: true }); // Sync database schema - force to recreate all tables
  })
  .then(() => {
    console.log('Database synchronized.');
  })
  .catch(err => {
    console.log('PostgreSQL connection error:', err);
  });

// Route mẫu
app.get('/', (req, res) => {
  res.send('FPT UniHub Backend is running!');
});

// Test Google OAuth configuration
app.get('/api/auth/google/test', (req, res) => {
  res.json({
    hasClientId: !!config.GOOGLE_CLIENT_ID && config.GOOGLE_CLIENT_ID !== 'your_google_client_id',
    hasClientSecret: !!config.GOOGLE_CLIENT_SECRET && config.GOOGLE_CLIENT_SECRET !== 'your_google_client_secret',
    callbackURL: 'http://localhost:5000/api/auth/google/callback',
    message: 'Kiểm tra Google OAuth configuration'
  });
});

app.use('/api/auth', require('./routes/auth'));
app.use('/api/todo', require('./routes/todo'));
app.use('/api/group', require('./routes/group'));
app.use('/api/thread', require('./routes/thread'));
app.use('/api/ai', require('./routes/ai_rag'));
app.use('/api/leaderboard', require('./routes/leaderboard'));
app.use('/api/forum', require('./routes/forum'));
app.use('/api/calendar', require('./routes/calendar'));
app.use('/api/file', require('./routes/file'));
app.use('/api/event', require('./routes/event'));
app.use('/api/exam', require('./routes/exam'));
app.use('/api/resource', require('./routes/resource'));
app.use('/api/mentor', require('./routes/mentor'));
app.use('/api/feedback', require('./routes/feedback'));
app.use('/api/progress', require('./routes/progress'));
app.use('/api/notification', require('./routes/notification'));
app.use('/api/classroom', require('./routes/classroom'));
app.use('/api/orchestrator', require('./routes/orchestrator'));
app.use('/api/prompt-enhancer', require('./routes/prompt_enhancer'));
app.use('/api/chatbot', require('./routes/chatbot'));
app.use('/uploads', express.static(require('path').join(__dirname, 'uploads')));

app.use(passport.initialize());

passport.use(new GoogleStrategy({
  clientID: config.GOOGLE_CLIENT_ID,
  clientSecret: config.GOOGLE_CLIENT_SECRET,
  callbackURL: 'http://localhost:5000/api/auth/google/callback',
}, async (accessToken, refreshToken, profile, done) => {
  try {
    let user = await User.findOne({ where: { googleId: profile.id } });
    if (!user) {
      user = await User.create({
        name: profile.displayName,
        email: profile.emails[0].value,
        googleId: profile.id,
        avatar: profile.photos[0].value,
      });
    }
    return done(null, user);
  } catch (err) {
    return done(err, null);
  }
}));

app.get('/api/auth/google', passport.authenticate('google', { scope: ['profile', 'email'] }));

app.get('/api/auth/google/callback', passport.authenticate('google', { session: false }), (req, res) => {
  const token = jwt.sign({ id: req.user.id, name: req.user.name, email: req.user.email }, config.JWT_SECRET, { expiresIn: '7d' });
  // Trả về token cho frontend (có thể redirect kèm token hoặc trả JSON)
  res.redirect(`http://localhost:5173/login?token=${token}`);
});

const server = http.createServer(app);
const io = new Server(server, { cors: { origin: '*' } });

// Start server
const PORT = config.PORT;
server.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`🤖 Integrated Chatbot ready at http://localhost:${PORT}/api/chatbot/health`);
});

// Socket.io chat nhóm
io.on('connection', (socket) => {
  socket.on('joinGroup', (groupId) => {
    socket.join(groupId);
  });
  socket.on('leaveGroup', (groupId) => {
    socket.leave(groupId);
  });
  socket.on('sendMessage', async ({ groupId, message }) => {
    io.to(groupId).emit('newMessage', message);
    // Gửi push notification tới các user trong nhóm (giả lập userIds)
    if (message && message.content) {
      await sendPushNotification({
        title: 'Tin nhắn mới trong nhóm',
        message: message.content,
        userIds: groupId ? [groupId] : [], // Cần thay bằng danh sách userIds thực tế
      });
    }
  });
});

 