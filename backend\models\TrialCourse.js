const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const TrialCourse = sequelize.define('TrialCourse', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    mentorId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'mentors',
        key: 'id',
      },
    },
  }, {
    tableName: 'trial_courses',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  TrialCourse.associate = (models) => {
    // TrialCourse belongs to Mentor
    TrialCourse.belongsTo(models.Mentor, {
      foreignKey: 'mentorId',
      as: 'mentor',
    });

    // TrialCourse belongs to many Users (students)
    TrialCourse.belongsToMany(models.User, {
      through: 'TrialCourseStudent',
      foreignKey: 'trialCourseId',
      as: 'students',
    });
  };

  return TrialCourse;
}; 