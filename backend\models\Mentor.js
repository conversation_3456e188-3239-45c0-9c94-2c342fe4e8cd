const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Mentor = sequelize.define('Mentor', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    bio: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    expertise: {
      type: DataTypes.ARRAY(DataTypes.STRING),
      defaultValue: [],
    },
    availableSlots: {
      type: DataTypes.JSONB,
      defaultValue: [],
    },
  }, {
    tableName: 'mentors',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  Mentor.associate = (models) => {
    // Mentor belongs to User
    Mentor.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return Mentor;
}; 