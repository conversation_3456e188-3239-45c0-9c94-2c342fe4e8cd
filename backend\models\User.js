const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const User = sequelize.define('User', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
      },
    },
    password: {
      type: DataTypes.STRING,
      allowNull: true, // chỉ dùng cho email/password
    },
    googleId: {
      type: DataTypes.STRING,
      allowNull: true, // chỉ dùng cho Google OAuth
    },
    avatar: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    role: {
      type: DataTypes.STRING,
      defaultValue: 'student',
      validate: {
        isIn: [['student', 'mentor', 'admin']],
      },
    },
    points: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    badges: {
      type: DataTypes.TEXT,
      defaultValue: '[]',
      get() {
        const value = this.getDataValue('badges');
        return value ? JSON.parse(value) : [];
      },
      set(value) {
        this.setDataValue('badges', JSON.stringify(value));
      },
    },
    learningProgress: {
      type: DataTypes.JSONB,
      defaultValue: {},
    },
  }, {
    tableName: 'users',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  User.associate = (models) => {
    // User has many Todos
    User.hasMany(models.Todo, {
      foreignKey: 'userId',
      as: 'todos',
    });

    // User belongs to many Groups (through UserGroup)
    User.belongsToMany(models.Group, {
      through: 'UserGroup',
      foreignKey: 'userId',
      as: 'groups',
    });

    // User has many Posts
    User.hasMany(models.Post, {
      foreignKey: 'userId',
      as: 'posts',
    });

    // User has many Comments
    User.hasMany(models.Comment, {
      foreignKey: 'userId',
      as: 'comments',
    });

    // User has many Events (as creator)
    User.hasMany(models.Event, {
      foreignKey: 'createdBy',
      as: 'createdEvents',
    });

    // User belongs to many Events (as participant)
    User.belongsToMany(models.Event, {
      through: 'EventParticipant',
      foreignKey: 'userId',
      as: 'participatedEvents',
    });

    // User has many Exams (as creator)
    User.hasMany(models.Exam, {
      foreignKey: 'createdBy',
      as: 'createdExams',
    });

    // User belongs to many Exams (as participant)
    User.belongsToMany(models.Exam, {
      through: 'ExamParticipant',
      foreignKey: 'userId',
      as: 'participatedExams',
    });

    // User has many Feedbacks
    User.hasMany(models.Feedback, {
      foreignKey: 'userId',
      as: 'feedbacks',
    });

    // User has one Mentor profile
    User.hasOne(models.Mentor, {
      foreignKey: 'userId',
      as: 'mentorProfile',
    });

    // User has many Notifications
    User.hasMany(models.Notification, {
      foreignKey: 'userId',
      as: 'notifications',
    });

    // User has many Progress records
    User.hasMany(models.Progress, {
      foreignKey: 'userId',
      as: 'progress',
    });

    // User has many Resources (as uploader)
    User.hasMany(models.Resource, {
      foreignKey: 'uploadedBy',
      as: 'uploadedResources',
    });

    // User belongs to many TrialCourses (as student)
    User.belongsToMany(models.TrialCourse, {
      through: 'TrialCourseStudent',
      foreignKey: 'userId',
      as: 'enrolledTrialCourses',
    });
  };

  return User;
}; 