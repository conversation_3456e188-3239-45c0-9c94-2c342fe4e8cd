export type { OpenChangeDetails as CollapsibleOpenChangeDetails } from '@zag-js/collapsible';
export { CollapsibleContent, type CollapsibleContentBaseProps, type CollapsibleContentProps, } from './collapsible-content';
export { CollapsibleContext, type CollapsibleContextProps } from './collapsible-context';
export { CollapsibleIndicator, type CollapsibleIndicatorBaseProps, type CollapsibleIndicatorProps, } from './collapsible-indicator';
export { CollapsibleRoot, type CollapsibleRootBaseProps, type CollapsibleRootProps } from './collapsible-root';
export { CollapsibleRootProvider, type CollapsibleRootProviderBaseProps, type CollapsibleRootProviderProps, } from './collapsible-root-provider';
export { CollapsibleTrigger, type CollapsibleTriggerBaseProps, type CollapsibleTriggerProps, } from './collapsible-trigger';
export { collapsibleAnatomy } from './collapsible.anatomy';
export { useCollapsible, type UseCollapsibleProps, type UseCollapsibleReturn } from './use-collapsible';
export { useCollapsibleContext, type UseCollapsibleContext } from './use-collapsible-context';
export * as Collapsible from './collapsible';
