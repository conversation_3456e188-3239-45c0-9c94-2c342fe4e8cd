{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"ty": {"title": "Type", "description": "Type of layer: Precomp.", "type": "number", "const": 0}, "ks": {"title": "Transform", "description": "Transform properties", "$ref": "#/helpers/transform", "type": "object"}, "ao": {"title": "Auto-Orient", "description": "Auto-Orient along path AE property.", "type": "number", "$ref": "#/helpers/boolean"}, "bm": {"title": "Blend Mode", "description": "Blend Mode", "type": "number", "$ref": "#/helpers/blendMode", "default": 0}, "ddd": {"title": "3d Layer", "description": "3d layer flag", "type": "number", "$ref": "#/helpers/boolean", "default": 0}, "ind": {"title": "Index", "description": "Layer index in AE. Used for parenting and expressions.", "type": "number"}, "cl": {"title": "Class", "description": "Parsed layer name used as html class on SVG/HTML renderer", "type": "string"}, "ln": {"title": "layer HTML ID", "description": "Parsed layer name used as html id on SVG/HTML renderer", "type": "string"}, "ip": {"title": "In Point", "description": "In Point of layer. Sets the initial frame of the layer.", "type": "number"}, "op": {"title": "Out Point", "description": "Out Point of layer. Sets the final frame of the layer.", "type": "number"}, "st": {"title": "Start Time", "description": "Start Time of layer. Sets the start time of the layer.", "type": "number"}, "nm": {"title": "Name", "description": "After Effects Layer Name. Used for expressions.", "type": "number"}, "hasMask": {"title": "Has Masks", "description": "Boolean when layer has a mask. Will be deprecated in favor of checking masksProperties.", "type": "number"}, "masksProperties": {"title": "Masks Properties", "description": "List of Masks", "items": {"$ref": "#/helpers/mask", "type": "object"}, "type": "array"}, "ef": {"title": "Effects", "description": "List of Effects", "items": {"$ref": "#/effects/index", "type": "object"}, "type": "array"}, "sr": {"title": "<PERSON><PERSON><PERSON>", "description": "Layer Time Stretching", "type": "number", "default": 1}, "parent": {"title": "Parent", "description": "Layer Parent. Uses ind of parent.", "type": "number"}, "refId": {"title": "Reference ID", "description": "id pointing to the source composition defined on 'assets' object", "type": "string"}, "tm": {"title": "Time Remapping", "description": "Comp's Time remapping", "type": "number", "$ref": "#/properties/valueKeyframed"}}}