'use client';
import { useState } from 'react';
import { useEvent } from '../../utils/use-event.js';
import { createListCollection } from './list-collection.js';

function useListCollection(props) {
  const { initialItems = [], filter, limit, ...collectionOptions } = props;
  const create = (items) => {
    return createListCollection({ ...collectionOptions, items });
  };
  const [collection, setCollectionImpl] = useState(
    () => create(limit != null ? initialItems.slice(0, limit) : initialItems)
  );
  const setCollection = useEvent((collection2) => {
    setCollectionImpl(limit == null ? collection2 : collection2.copy(collection2.items.slice(0, limit)));
  });
  return {
    collection,
    filter: (inputValue) => {
      if (!filter) return;
      const filtered = create(initialItems).filter((itemString) => filter(itemString, inputValue));
      setCollection(filtered);
    },
    set: useEvent((items) => {
      setCollection(create(items));
    }),
    reset: useEvent(() => {
      setCollection(create(initialItems));
    }),
    clear: useEvent(() => {
      setCollection(create([]));
    }),
    insert: useEvent((index, ...items) => {
      setCollection(collection.insert(index, ...items));
    }),
    insertBefore: useEvent((value, ...items) => {
      setCollection(collection.insertBefore(value, ...items));
    }),
    insertAfter: useEvent((value, ...items) => {
      setCollection(collection.insertAfter(value, ...items));
    }),
    remove: useEvent((...itemOrValues) => {
      setCollection(collection.remove(...itemOrValues));
    }),
    move: useEvent((value, to) => {
      setCollection(collection.move(value, to));
    }),
    moveBefore: useEvent((value, ...values) => {
      setCollection(collection.moveBefore(value, ...values));
    }),
    moveAfter: useEvent((value, ...values) => {
      setCollection(collection.moveAfter(value, ...values));
    }),
    reorder: useEvent((from, to) => {
      setCollection(collection.reorder(from, to));
    }),
    append: useEvent((...items) => {
      setCollection(collection.append(...items));
    }),
    prepend: useEvent((...items) => {
      setCollection(collection.prepend(...items));
    }),
    update: useEvent((value, item) => {
      setCollection(collection.update(value, item));
    })
  };
}

export { useListCollection };
