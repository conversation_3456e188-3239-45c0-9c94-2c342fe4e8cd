export type { StatusChangeDetails } from '@zag-js/avatar';
export { AvatarContext as Context, type AvatarContextProps as ContextProps } from './avatar-context';
export { AvatarFallback as Fallback, type AvatarFallbackBaseProps as FallbackBaseProps, type AvatarFallbackProps as FallbackProps, } from './avatar-fallback';
export { AvatarImage as Image, type AvatarImageBaseProps as ImageBaseProps, type AvatarImageProps as ImageProps, } from './avatar-image';
export { AvatarRoot as Root, type AvatarRootBaseProps as RootBaseProps, type AvatarRootProps as RootProps, } from './avatar-root';
export { AvatarRootProvider as RootProvider, type AvatarRootProviderBaseProps as RootProviderBaseProps, type AvatarRootProviderProps as RootProviderProps, } from './avatar-root-provider';
