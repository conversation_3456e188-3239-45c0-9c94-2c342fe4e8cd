'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const jsxRuntime = require('react/jsx-runtime');
const react$1 = require('@zag-js/react');
const react = require('react');
const createSplitProps = require('../../utils/create-split-props.cjs');
const factory = require('../factory.cjs');
const useAngleSliderContext = require('./use-angle-slider-context.cjs');

const AngleSliderMarker = react.forwardRef((props, ref) => {
  const [itemProps, localProps] = createSplitProps.createSplitProps()(props, ["value"]);
  const angleSlider = useAngleSliderContext.useAngleSliderContext();
  const mergedProps = react$1.mergeProps(angleSlider.getMarkerProps(itemProps), localProps);
  return /* @__PURE__ */ jsxRuntime.jsx(factory.ark.div, { ...mergedProps, ref });
});
AngleSliderMarker.displayName = "AngleSliderMarker";

exports.AngleSliderMarker = AngleSliderMarker;
