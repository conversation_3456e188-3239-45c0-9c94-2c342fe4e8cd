const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Thread = sequelize.define('Thread', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    groupId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'groups',
        key: 'id',
      },
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    summary: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
  }, {
    tableName: 'threads',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  Thread.associate = (models) => {
    // Thread belongs to Group
    Thread.belongsTo(models.Group, {
      foreignKey: 'groupId',
      as: 'group',
    });

    // Thread has many Messages
    Thread.hasMany(models.Message, {
      foreignKey: 'threadId',
      as: 'messages',
    });
  };

  return Thread;
}; 