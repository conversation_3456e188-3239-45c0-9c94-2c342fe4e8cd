const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Progress = sequelize.define('Progress', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    course: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    percent: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 0,
        max: 100,
      },
    },
  }, {
    tableName: 'progress',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  Progress.associate = (models) => {
    // Progress belongs to User
    Progress.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return Progress;
}; 