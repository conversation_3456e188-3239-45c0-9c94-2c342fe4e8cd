'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const react = require('react');
const useEvent = require('../../utils/use-event.cjs');
const listCollection = require('./list-collection.cjs');

function useListCollection(props) {
  const { initialItems = [], filter, limit, ...collectionOptions } = props;
  const create = (items) => {
    return listCollection.createListCollection({ ...collectionOptions, items });
  };
  const [collection, setCollectionImpl] = react.useState(
    () => create(limit != null ? initialItems.slice(0, limit) : initialItems)
  );
  const setCollection = useEvent.useEvent((collection2) => {
    setCollectionImpl(limit == null ? collection2 : collection2.copy(collection2.items.slice(0, limit)));
  });
  return {
    collection,
    filter: (inputValue) => {
      if (!filter) return;
      const filtered = create(initialItems).filter((itemString) => filter(itemString, inputValue));
      setCollection(filtered);
    },
    set: useEvent.useEvent((items) => {
      setCollection(create(items));
    }),
    reset: useEvent.useEvent(() => {
      setCollection(create(initialItems));
    }),
    clear: useEvent.useEvent(() => {
      setCollection(create([]));
    }),
    insert: useEvent.useEvent((index, ...items) => {
      setCollection(collection.insert(index, ...items));
    }),
    insertBefore: useEvent.useEvent((value, ...items) => {
      setCollection(collection.insertBefore(value, ...items));
    }),
    insertAfter: useEvent.useEvent((value, ...items) => {
      setCollection(collection.insertAfter(value, ...items));
    }),
    remove: useEvent.useEvent((...itemOrValues) => {
      setCollection(collection.remove(...itemOrValues));
    }),
    move: useEvent.useEvent((value, to) => {
      setCollection(collection.move(value, to));
    }),
    moveBefore: useEvent.useEvent((value, ...values) => {
      setCollection(collection.moveBefore(value, ...values));
    }),
    moveAfter: useEvent.useEvent((value, ...values) => {
      setCollection(collection.moveAfter(value, ...values));
    }),
    reorder: useEvent.useEvent((from, to) => {
      setCollection(collection.reorder(from, to));
    }),
    append: useEvent.useEvent((...items) => {
      setCollection(collection.append(...items));
    }),
    prepend: useEvent.useEvent((...items) => {
      setCollection(collection.prepend(...items));
    }),
    update: useEvent.useEvent((value, item) => {
      setCollection(collection.update(value, item));
    })
  };
}

exports.useListCollection = useListCollection;
