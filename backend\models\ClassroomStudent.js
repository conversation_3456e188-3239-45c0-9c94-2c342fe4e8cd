const { DataTypes } = require('sequelize');
const { sequelize } = require('./index');

const ClassroomStudent = sequelize.define('ClassroomStudent', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  classroomId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Classrooms',
      key: 'id'
    }
  },
  studentId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  enrolledAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'dropped'),
    defaultValue: 'active'
  },
  grade: {
    type: DataTypes.DECIMAL(3, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 10
    }
  },
  attendance: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object tracking attendance'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'classroom_students',
  timestamps: true,
  indexes: [
    {
      fields: ['classroomId', 'studentId'],
      unique: true
    },
    {
      fields: ['studentId']
    },
    {
      fields: ['status']
    }
  ]
});

module.exports = ClassroomStudent;
