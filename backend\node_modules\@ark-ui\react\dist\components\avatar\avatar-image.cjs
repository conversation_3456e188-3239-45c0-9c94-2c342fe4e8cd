'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const jsxRuntime = require('react/jsx-runtime');
const react$1 = require('@zag-js/react');
const react = require('react');
const factory = require('../factory.cjs');
const useAvatarContext = require('./use-avatar-context.cjs');

const AvatarImage = react.forwardRef((props, ref) => {
  const avatar = useAvatarContext.useAvatarContext();
  const mergedProps = react$1.mergeProps(avatar.getImageProps(), props);
  return /* @__PURE__ */ jsxRuntime.jsx(factory.ark.img, { ...mergedProps, ref });
});
AvatarImage.displayName = "AvatarImage";

exports.AvatarImage = AvatarImage;
