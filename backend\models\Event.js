const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Event = sequelize.define('Event', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    start: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    end: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'events',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  Event.associate = (models) => {
    // Event belongs to User (creator)
    Event.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });

    // Event belongs to many Users (participants)
    Event.belongsToMany(models.User, {
      through: 'EventParticipant',
      foreignKey: 'eventId',
      as: 'participants',
    });
  };

  return Event;
}; 