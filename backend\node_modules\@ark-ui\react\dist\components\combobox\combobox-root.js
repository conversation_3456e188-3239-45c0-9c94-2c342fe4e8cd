'use client';
import { jsx } from 'react/jsx-runtime';
import { mergeProps } from '@zag-js/react';
import { forwardRef } from 'react';
import { createSplitProps } from '../../utils/create-split-props.js';
import { ark } from '../factory.js';
import { splitPresenceProps } from '../presence/split-presence-props.js';
import { usePresence } from '../presence/use-presence.js';
import { PresenceProvider } from '../presence/use-presence-context.js';
import { useCombobox } from './use-combobox.js';
import { ComboboxProvider } from './use-combobox-context.js';

const ComboboxImpl = (props, ref) => {
  const [presenceProps, comboboxProps] = splitPresenceProps(props);
  const [useComboboxProps, localProps] = createSplitProps()(comboboxProps, [
    "allowCustomValue",
    "autoFocus",
    "closeOnSelect",
    "collection",
    "composite",
    "defaultHighlightedValue",
    "defaultInputValue",
    "defaultOpen",
    "defaultValue",
    "disabled",
    "disableLayer",
    "form",
    "highlightedValue",
    "id",
    "ids",
    "inputBehavior",
    "inputValue",
    "invalid",
    "loopFocus",
    "multiple",
    "name",
    "navigate",
    "onFocusOutside",
    "onHighlightChange",
    "onInputValueChange",
    "onInteractOutside",
    "onOpenChange",
    "onPointerDownOutside",
    "onSelect",
    "onValueChange",
    "open",
    "openOnChange",
    "openOnClick",
    "openOnKeyPress",
    "placeholder",
    "positioning",
    "readOnly",
    "required",
    "scrollToIndexFn",
    "selectionBehavior",
    "translations",
    "value"
  ]);
  const combobox = useCombobox(useComboboxProps);
  const presence = usePresence(mergeProps({ present: combobox.open }, presenceProps));
  const mergedProps = mergeProps(combobox.getRootProps(), localProps);
  return /* @__PURE__ */ jsx(ComboboxProvider, { value: combobox, children: /* @__PURE__ */ jsx(PresenceProvider, { value: presence, children: /* @__PURE__ */ jsx(ark.div, { ...mergedProps, ref }) }) });
};
const ComboboxRoot = forwardRef(ComboboxImpl);

export { ComboboxRoot };
