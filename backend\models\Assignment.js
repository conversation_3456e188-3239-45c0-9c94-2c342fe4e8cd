const { DataTypes } = require('sequelize');
const { sequelize } = require('./index');

const Assignment = sequelize.define('Assignment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  classroomId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Classrooms',
      key: 'id'
    }
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  instructions: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  type: {
    type: DataTypes.ENUM('homework', 'project', 'quiz', 'exam', 'reading', 'practice'),
    defaultValue: 'homework'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  dueDate: {
    type: DataTypes.DATE,
    allowNull: false
  },
  assignedDate: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  maxPoints: {
    type: DataTypes.INTEGER,
    defaultValue: 100,
    validate: {
      min: 0
    }
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  autoCreateTodo: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Automatically create todo items for students'
  },
  attachments: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON array of file attachments'
  },
  rubric: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object containing grading rubric'
  },
  settings: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional assignment settings'
  },
  createdBy: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  createdAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updatedAt: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'assignments',
  timestamps: true,
  indexes: [
    {
      fields: ['classroomId']
    },
    {
      fields: ['dueDate']
    },
    {
      fields: ['type']
    },
    {
      fields: ['priority']
    },
    {
      fields: ['createdBy']
    }
  ]
});

module.exports = Assignment;
