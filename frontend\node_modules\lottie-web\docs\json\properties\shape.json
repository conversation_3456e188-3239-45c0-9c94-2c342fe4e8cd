{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"k": {"description": "Property Value", "extended_name": "Value", "type": "object", "$ref": "#/properties/shapeProp"}, "x": {"description": "Property Expression. An AE expression that modifies the value.", "extended_name": "Expression", "type": "string"}, "ix": {"description": "Property Index. Used for expressions.", "extended_name": "Property Index", "type": "string"}, "a": {"description": "Defines if property is animated", "extended_name": "Animated", "type": "number"}}}