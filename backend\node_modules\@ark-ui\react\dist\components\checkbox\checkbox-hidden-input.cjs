'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const jsxRuntime = require('react/jsx-runtime');
const react$1 = require('@zag-js/react');
const react = require('react');
const factory = require('../factory.cjs');
const useFieldContext = require('../field/use-field-context.cjs');
const useCheckboxContext = require('./use-checkbox-context.cjs');

const CheckboxHiddenInput = react.forwardRef((props, ref) => {
  const checkbox = useCheckboxContext.useCheckboxContext();
  const mergedProps = react$1.mergeProps(checkbox.getHiddenInputProps(), props);
  const field = useFieldContext.useFieldContext();
  return /* @__PURE__ */ jsxRuntime.jsx(factory.ark.input, { "aria-describedby": field?.ariaDescribedby, ...mergedProps, ref });
});
CheckboxHiddenInput.displayName = "CheckboxHiddenInput";

exports.CheckboxHiddenInput = CheckboxHiddenInput;
