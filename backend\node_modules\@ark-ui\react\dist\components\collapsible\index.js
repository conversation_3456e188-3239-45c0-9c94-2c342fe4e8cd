export { CollapsibleContent } from './collapsible-content.js';
export { CollapsibleContext } from './collapsible-context.js';
export { CollapsibleIndicator } from './collapsible-indicator.js';
export { CollapsibleRoot } from './collapsible-root.js';
export { CollapsibleRootProvider } from './collapsible-root-provider.js';
export { CollapsibleTrigger } from './collapsible-trigger.js';
export { useCollapsible } from './use-collapsible.js';
export { useCollapsibleContext } from './use-collapsible-context.js';
import * as collapsible from './collapsible.js';
export { collapsible as Collapsible };
export { anatomy as collapsibleAnatomy } from '@zag-js/collapsible';
