const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { Todo, User, Group, TrialCourse } = require('../models');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const config = require('../config');

// Middleware xác thực JWT
function auth(req, res, next) {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token) return res.status(401).json({ message: 'No token' });
  try {
    const decoded = jwt.verify(token, config.JWT_SECRET);
    req.userId = decoded.id;
    next();
  } catch {
    res.status(401).json({ message: 'Invalid token' });
  }
}

// Function map cho AI gọi CRUD
const ragFunctions = {
  async getTodos({ userId }) {
    const todos = await Todo.findAll({ where: { userId } });
    return todos.map(t => ({ title: t.title, deadline: t.deadline, isDone: t.isDone }));
  },
  async createTodo({ userId, title, deadline }) {
    const todo = await Todo.create({ userId, title, deadline });
    return { success: true, todo: { title: todo.title, deadline: todo.deadline } };
  },
  async updateTodo({ userId, title, isDone }) {
    const todo = await Todo.findOne({ where: { userId, title } });
    if (todo) {
      await todo.update({ isDone });
      return { success: true, todo };
    }
    return { success: false };
  },
  async deleteTodo({ userId, title }) {
    const todo = await Todo.findOne({ where: { userId, title } });
    if (todo) {
      await todo.destroy();
      return { success: true };
    }
    return { success: false };
  },
  // GROUP CRUD
  async getGroups({ userId }) {
    const user = await User.findByPk(userId, { include: { model: Group, as: 'groups' } });
    return user ? user.groups.map(g => ({ name: g.name, description: g.description })) : [];
  },
  async createGroup({ userId, name, description }) {
    const group = await Group.create({ name, description });
    await group.addMember(userId);
    return { success: true, group: { name: group.name, description: group.description } };
  },
  async joinGroup({ userId, name }) {
    const group = await Group.findOne({ where: { name } });
    if (group) {
      await group.addMember(userId);
      return { success: true, group };
    }
    return { success: false };
  },
  async leaveGroup({ userId, name }) {
    const group = await Group.findOne({ where: { name } });
    if (group) {
      await group.removeMember(userId);
      return { success: true };
    }
    return { success: false };
  },
  // FILE CRUD
  async getFiles() {
    const uploadDir = path.join(__dirname, '../uploads');
    const files = fs.readdirSync(uploadDir).map(f => ({ filename: f, url: `/api/file/download/${f}` }));
    return files;
  },
  async deleteFile({ filename }) {
    const uploadDir = path.join(__dirname, '../uploads');
    const filePath = path.join(uploadDir, filename);
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      return { success: true };
    }
    return { success: false, message: 'File not found' };
  },
  // LEARNING PROGRESS CRUD
  async getLearningProgress({ userId }) {
    const user = await User.findByPk(userId);
    return user ? user.learningProgress || {} : {};
  },
  async updateLearningProgress({ userId, progress }) {
    const user = await User.findByPk(userId);
    if (user) {
      await user.update({ learningProgress: progress });
      return { success: true };
    }
    return { success: false };
  },
  // QUIZ/TRIALCOURSE CRUD
  async getTrialCourses() {
    const courses = await TrialCourse.findAll();
    return courses.map(c => ({ title: c.title, description: c.description }));
  },
  async getTrialQuiz({ major }) {
    const course = await TrialCourse.findOne({ where: { title: major } });
    return course ? { title: course.title, description: course.description } : null;
  },
  // TODO: Thêm các function cho nhóm, tài liệu, tiến trình học, ...
};

// Endpoint AI RAG Chatbot
router.post('/rag-chatbot', auth, async (req, res) => {
  const { prompt } = req.body;
  const userId = req.userId;
  try {
    // Gọi Gemini/GPT-4 function calling để phân tích intent và gọi function phù hợp
    // DEMO: Dùng từ khóa đơn giản, thực tế nên dùng OpenAI function calling hoặc Gemini function calling
    let result;
    if (/tạo to-do|add to-do|create to-do/i.test(prompt)) {
      // Ví dụ: "Tạo to-do nộp bài toán vào thứ 3"
      const title = prompt.split('to-do')[1]?.trim() || 'To-do mới';
      result = await ragFunctions.createTodo({ userId, title });
    } else if (/xem to-do|show to-do|list to-do/i.test(prompt)) {
      result = await ragFunctions.getTodos({ userId });
    } else if (/hoàn thành|done|mark as done/i.test(prompt)) {
      // Ví dụ: "Đánh dấu hoàn thành to-do nộp bài toán"
      const title = prompt.split('to-do')[1]?.trim() || '';
      result = await ragFunctions.updateTodo({ userId, title, isDone: true });
    } else if (/xóa to-do|delete to-do/i.test(prompt)) {
      const title = prompt.split('to-do')[1]?.trim() || '';
      result = await ragFunctions.deleteTodo({ userId, title });
    } else if (/tạo nhóm|create group/i.test(prompt)) {
      const name = prompt.split('nhóm')[1]?.trim() || 'Nhóm mới';
      result = await ragFunctions.createGroup({ userId, name });
    } else if (/xem nhóm|show group|list group/i.test(prompt)) {
      result = await ragFunctions.getGroups({ userId });
    } else if (/tham gia nhóm|join group/i.test(prompt)) {
      const name = prompt.split('nhóm')[1]?.trim() || '';
      result = await ragFunctions.joinGroup({ userId, name });
    } else if (/rời nhóm|leave group/i.test(prompt)) {
      const name = prompt.split('nhóm')[1]?.trim() || '';
      result = await ragFunctions.leaveGroup({ userId, name });
    } else if (/xem file|show file|list file/i.test(prompt)) {
      result = await ragFunctions.getFiles();
    } else if (/xóa file|delete file/i.test(prompt)) {
      const filename = prompt.split('file')[1]?.trim() || '';
      result = await ragFunctions.deleteFile({ filename });
    } else if (/xem tiến trình|show progress|learning progress/i.test(prompt)) {
      result = await ragFunctions.getLearningProgress({ userId });
    } else if (/cập nhật tiến trình|update progress/i.test(prompt)) {
      // Ví dụ: "Cập nhật tiến trình {"cntt": 80}"
      const progress = (() => { try { return JSON.parse(prompt.split('tiến trình')[1]); } catch { return {}; } })();
      result = await ragFunctions.updateLearningProgress({ userId, progress });
    } else if (/xem quiz|xem học thử|show trial|list trial/i.test(prompt)) {
      result = await ragFunctions.getTrialCourses();
    } else if (/quiz ngành|quiz trial|quiz major/i.test(prompt)) {
      const major = prompt.split('ngành')[1]?.trim() || '';
      result = await ragFunctions.getTrialQuiz({ major });
    } else {
      // Nếu không phải CRUD, gọi Gemini/GPT-4 trả lời tự do
      const apiKey = config.GOOGLE_API_KEY || process.env.OPENAI_API_KEY;
      if (!apiKey) return res.status(400).json({ message: 'Missing AI API key' });
      let text = '';
      if (config.GOOGLE_API_KEY) {
        const geminiRes = await axios.post(
          'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=' + apiKey,
          { contents: [{ parts: [{ text: prompt }] }] }
        );
        text = geminiRes.data.candidates?.[0]?.content?.parts?.[0]?.text || 'Không có phản hồi từ Gemini.';
      } else {
        const openaiRes = await axios.post(
          'https://api.openai.com/v1/chat/completions',
          {
            model: 'gpt-4',
            messages: [{ role: 'user', content: prompt }],
          },
          { headers: { Authorization: `Bearer ${apiKey}` } }
        );
        text = openaiRes.data.choices?.[0]?.message?.content || 'Không có phản hồi từ OpenAI.';
      }
      result = { text };
    }
    res.json(result);
  } catch (err) {
    res.status(500).json({ message: 'AI RAG error', error: err.message });
  }
});

// Learning Planner endpoint
router.post('/learning-planner', async (req, res) => {
  try {
    const { message, userId, conversationHistory } = req.body;

    // Enhanced prompt for learning plan generation
    const learningPlannerPrompt = `
You are an AI Learning Planner for FPT University students. Your role is to create personalized learning roadmaps and study plans.

User message: "${message}"

Based on the user's request, analyze if they want to:
1. Create a learning roadmap for a specific subject/skill
2. Prepare for an exam or assignment
3. Develop a skill over a specific timeframe
4. Get study recommendations

If the user wants a learning plan, generate a structured response that includes:
- A conversational response acknowledging their request
- A detailed learning plan with weekly breakdown
- Specific topics and milestones
- Estimated time commitments
- Study tips and resources

If this is a learning plan request, also include a JSON object with this structure:
{
  "title": "Learning Plan Title",
  "duration": "X weeks/months",
  "topics": ["Week 1: Topic", "Week 2: Topic", ...],
  "totalHours": estimated_hours,
  "difficulty": "beginner/intermediate/advanced",
  "goals": ["Goal 1", "Goal 2", ...]
}

Respond in Vietnamese and be encouraging and supportive.
`;

    const response = await axios.post(
      'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=' + config.GOOGLE_API_KEY,
      {
        contents: [{
          parts: [{
            text: learningPlannerPrompt
          }]
        }]
      }
    );

    const aiResponse = response.data.candidates[0].content.parts[0].text;

    // Try to extract learning plan JSON from response
    let learningPlan = null;
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      try {
        learningPlan = JSON.parse(jsonMatch[0]);
      } catch (e) {
        console.log('Could not parse learning plan JSON');
      }
    }

    // Clean response text (remove JSON if present)
    const cleanResponse = aiResponse.replace(/\{[\s\S]*\}/, '').trim();

    res.json({
      response: cleanResponse,
      learningPlan: learningPlan,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Learning Planner error:', error);
    res.status(500).json({
      error: 'Có lỗi xảy ra khi tạo lộ trình học tập',
      details: error.message
    });
  }
});

// Create learning todos from plan
router.post('/create-learning-todos', async (req, res) => {
  try {
    const { learningPlan, userId } = req.body;

    if (!learningPlan || !learningPlan.topics) {
      return res.status(400).json({ error: 'Invalid learning plan' });
    }

    const todos = [];
    const startDate = new Date();

    // Create todos for each topic/week
    for (let i = 0; i < learningPlan.topics.length; i++) {
      const topic = learningPlan.topics[i];
      const dueDate = new Date(startDate);
      dueDate.setDate(startDate.getDate() + (i + 1) * 7); // Each topic is due in a week

      const todo = await Todo.create({
        title: `${learningPlan.title} - ${topic}`,
        description: `Học tập theo lộ trình: ${topic}`,
        priority: 'medium',
        deadline: dueDate,
        userId: userId,
        category: 'learning',
        isDone: false
      });

      todos.push(todo);
    }

    res.json({
      message: 'Learning todos created successfully',
      todosCreated: todos.length,
      todos: todos
    });

  } catch (error) {
    console.error('Error creating learning todos:', error);
    res.status(500).json({
      error: 'Có lỗi xảy ra khi tạo todo items',
      details: error.message
    });
  }
});

module.exports = router;