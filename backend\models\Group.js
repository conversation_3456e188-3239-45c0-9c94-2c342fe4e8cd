const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Group = sequelize.define('Group', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    avatar: {
      type: DataTypes.STRING,
      allowNull: true,
    },
  }, {
    tableName: 'groups',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  Group.associate = (models) => {
    // Group belongs to many Users (through UserGroup)
    Group.belongsToMany(models.User, {
      through: 'UserGroup',
      foreignKey: 'groupId',
      as: 'members',
    });

    // Group has many Threads
    Group.hasMany(models.Thread, {
      foreignKey: 'groupId',
      as: 'threads',
    });
  };

  return Group;
}; 