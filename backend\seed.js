// Seed dữ liệu "giống thật" cho toàn bộ hệ thống vào PostgreSQL
const { sequelize, User, Group, Thread, Message, Post, Comment, Event, Exam, Feedback, Mentor, Notification, Progress, Resource, Todo, TrialCourse } = require('./models');
const faker = require('faker');
const config = require('./config');

function randomPick(arr, n) {
  return faker.helpers.shuffle(arr).slice(0, n);
}

async function seed() {
  try {
    await sequelize.authenticate();
    console.log('Đã kết nối PostgreSQL!');
    
    // Sync database
    await sequelize.sync({ force: true });
    console.log('Đã sync database!');

    // 1. User
    const users = await User.bulkCreate(Array.from({ length: 30 }, (_, i) => ({
      name: faker.name.findName(),
      email: faker.internet.email(),
      password: faker.internet.password(),
      avatar: faker.image.avatar(),
      role: faker.random.arrayElement(['student', 'mentor', 'admin']),
      points: faker.datatype.number({ min: 0, max: 100 }),
      badges: randomPick(['Top học viên', 'Chuyên cần', 'Hỗ trợ tốt', 'Thành viên tích cực'], faker.datatype.number({ min: 0, max: 3 })),
      createdAt: faker.date.past(1)
    })));

    // 2. Mentor
    const mentors = await Mentor.bulkCreate(users.filter(u => u.role === 'mentor').map(u => ({
      userId: u.id,
      bio: faker.lorem.sentences(2),
      expertise: randomPick(['Toán', 'Lý', 'Hóa', 'Anh', 'Tin học', 'Sinh'], faker.datatype.number({ min: 1, max: 3 })),
      availableSlots: Array.from({ length: 3 }, () => ({ start: faker.date.future(), end: faker.date.future() })),
      createdAt: faker.date.past(1)
    })));

    // 3. Group
    const groups = await Group.bulkCreate(Array.from({ length: 8 }, () => ({
      name: faker.company.companyName(),
      description: faker.company.catchPhrase(),
      avatar: faker.image.avatar(),
      createdAt: faker.date.past(1)
    })));

    // Add members to groups
    for (const group of groups) {
      const memberCount = faker.datatype.number({ min: 3, max: 10 });
      const members = randomPick(users, memberCount);
      await group.addMembers(members);
    }

    // 4. Thread
    const threads = await Thread.bulkCreate(groups.flatMap(g => Array.from({ length: faker.datatype.number({ min: 2, max: 5 }) }, () => ({
      groupId: g.id,
      title: faker.lorem.sentence(),
      summary: faker.lorem.sentences(2),
      createdAt: faker.date.past(1)
    }))));

    // 5. Message
    const messages = await Message.bulkCreate(threads.flatMap(t => Array.from({ length: faker.datatype.number({ min: 3, max: 10 }) }, () => ({
      threadId: t.id,
      senderId: faker.random.arrayElement(users).id,
      content: faker.lorem.sentences(2),
      isAI: faker.datatype.boolean(),
      isAnonymous: faker.datatype.boolean(),
      createdAt: faker.date.past(1)
    }))));

    // 6. Post
    const posts = await Post.bulkCreate(Array.from({ length: 20 }, () => {
      const user = faker.random.arrayElement(users);
      return {
        userId: user.id,
        title: faker.lorem.sentence(),
        content: faker.lorem.paragraphs(2),
        votes: faker.datatype.number({ min: 0, max: 50 }),
        createdAt: faker.date.past(1)
      };
    }));

    // 7. Comment
    const comments = await Comment.bulkCreate(posts.flatMap(p => Array.from({ length: faker.datatype.number({ min: 1, max: 5 }) }, () => ({
      userId: faker.random.arrayElement(users).id,
      postId: p.id,
      content: faker.lorem.sentences(2),
      createdAt: faker.date.past(1)
    }))));

    // 8. Event
    const events = await Event.bulkCreate(Array.from({ length: 10 }, () => {
      const createdBy = faker.random.arrayElement(users);
      return {
        title: faker.company.bsBuzz() + ' ' + faker.company.bsNoun(),
        description: faker.lorem.sentences(2),
        start: faker.date.future(),
        end: faker.date.future(),
        createdBy: createdBy.id,
        createdAt: faker.date.past(1)
      };
    }));

    // Add participants to events
    for (const event of events) {
      const participants = randomPick(users, faker.datatype.number({ min: 5, max: 15 }));
      await event.addParticipants(participants);
    }

    // 9. Exam
    const exams = await Exam.bulkCreate(Array.from({ length: 8 }, () => {
      const createdBy = faker.random.arrayElement(users);
      return {
        title: faker.company.bsAdjective() + ' ' + faker.company.bsNoun(),
        description: faker.lorem.sentences(2),
        date: faker.date.future(),
        duration: faker.datatype.number({ min: 30, max: 180 }),
        createdBy: createdBy.id,
        createdAt: faker.date.past(1)
      };
    }));

    // Add participants to exams
    for (const exam of exams) {
      const participants = randomPick(users, faker.datatype.number({ min: 5, max: 20 }));
      await exam.addParticipants(participants);
    }

    // 10. Feedback
    await Feedback.bulkCreate(Array.from({ length: 30 }, () => ({
      userId: faker.random.arrayElement(users).id,
      content: faker.lorem.sentences(2),
      rating: faker.datatype.number({ min: 1, max: 5 }),
      createdAt: faker.date.past(1)
    })));

    // 11. Notification
    await Notification.bulkCreate(Array.from({ length: 50 }, () => ({
      userId: faker.random.arrayElement(users).id,
      content: faker.lorem.sentence(),
      read: faker.datatype.boolean(),
      createdAt: faker.date.past(1)
    })));

    // 12. Progress
    await Progress.bulkCreate(users.map(u => ({
      userId: u.id,
      course: faker.commerce.productName(),
      percent: faker.datatype.number({ min: 0, max: 100 }),
      updatedAt: faker.date.recent()
    })));

    // 13. Resource
    await Resource.bulkCreate(Array.from({ length: 15 }, () => ({
      title: faker.commerce.productName(),
      url: faker.internet.url(),
      description: faker.lorem.sentences(2),
      uploadedBy: faker.random.arrayElement(users).id,
      createdAt: faker.date.past(1)
    })));

    // 14. Todo
    await Todo.bulkCreate(users.flatMap(u => Array.from({ length: faker.datatype.number({ min: 1, max: 5 }) }, () => ({
      userId: u.id,
      title: faker.lorem.sentence(),
      isDone: faker.datatype.boolean(),
      createdAt: faker.date.past(1)
    }))));

    // 15. TrialCourse
    const trialCourses = await TrialCourse.bulkCreate(Array.from({ length: 5 }, () => ({
      title: faker.company.catchPhrase(),
      description: faker.lorem.sentences(2),
      createdAt: faker.date.past(1)
    })));

    // Add students to trial courses
    for (const course of trialCourses) {
      const students = randomPick(users, faker.datatype.number({ min: 5, max: 15 }));
      await course.addStudents(students);
    }

    console.log('Seed dữ liệu phong phú thành công!');
    await sequelize.close();
  } catch (err) {
    console.error('Seed error:', err);
    await sequelize.close();
    process.exit(1);
  }
}

seed(); 