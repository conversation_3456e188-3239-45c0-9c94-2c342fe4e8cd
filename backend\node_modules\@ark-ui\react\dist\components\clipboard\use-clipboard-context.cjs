'use client';
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

const createContext = require('../../utils/create-context.cjs');

const [ClipboardProvider, useClipboardContext] = createContext.createContext({
  name: "ClipboardContext",
  hookName: "useClipboardContext",
  providerName: "<ClipboardProvider />"
});

exports.ClipboardProvider = ClipboardProvider;
exports.useClipboardContext = useClipboardContext;
