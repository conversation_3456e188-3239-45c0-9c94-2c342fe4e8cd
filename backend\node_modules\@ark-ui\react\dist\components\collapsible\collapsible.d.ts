export type { OpenChangeDetails } from '@zag-js/collapsible';
export { CollapsibleContent as Content, type CollapsibleContentBaseProps as ContentBaseProps, type CollapsibleContentProps as ContentProps, } from './collapsible-content';
export { CollapsibleContext as Context, type CollapsibleContextProps as ContextProps } from './collapsible-context';
export { CollapsibleIndicator as Indicator, type CollapsibleIndicatorBaseProps as IndicatorBaseProps, type CollapsibleIndicatorProps as IndicatorProps, } from './collapsible-indicator';
export { CollapsibleRoot as Root, type CollapsibleRootBaseProps as RootBaseProps, type CollapsibleRootProps as RootProps, } from './collapsible-root';
export { CollapsibleRootProvider as RootProvider, type CollapsibleRootProviderBaseProps as RootProviderBaseProps, type CollapsibleRootProviderProps as RootProviderProps, } from './collapsible-root-provider';
export { CollapsibleTrigger as Trigger, type CollapsibleTriggerBaseProps as TriggerBaseProps, type CollapsibleTriggerProps as TriggerProps, } from './collapsible-trigger';
