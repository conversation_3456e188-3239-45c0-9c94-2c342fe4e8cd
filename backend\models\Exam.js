const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Exam = sequelize.define('Exam', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    duration: {
      type: DataTypes.INTEGER, // minutes
      allowNull: false,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
  }, {
    tableName: 'exams',
    timestamps: true,
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  });

  // Associations
  Exam.associate = (models) => {
    // Exam belongs to User (creator)
    Exam.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });

    // Exam belongs to many Users (participants)
    Exam.belongsToMany(models.User, {
      through: 'ExamParticipant',
      foreignKey: 'examId',
      as: 'participants',
    });
  };

  return Exam;
}; 