export { AvatarContext } from './avatar-context.js';
export { AvatarFallback } from './avatar-fallback.js';
export { AvatarImage } from './avatar-image.js';
export { AvatarRoot } from './avatar-root.js';
export { AvatarRootProvider } from './avatar-root-provider.js';
export { useAvatar } from './use-avatar.js';
export { useAvatarContext } from './use-avatar-context.js';
import * as avatar from './avatar.js';
export { avatar as Avatar };
export { anatomy as avatarAnatomy } from '@zag-js/avatar';
